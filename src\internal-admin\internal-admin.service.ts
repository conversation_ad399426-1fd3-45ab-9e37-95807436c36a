import { BadRequestException, Injectable } from '@nestjs/common';
import { Span } from 'nestjs-ddtrace';

import { AppService } from '../app/app.service';
import { SettingsService } from '../common/services/settings/settings.service';
import { TGetUserSettings } from '../common/services/settings/types';
import { UserService } from '../user/user.service';

@Injectable()
@Span()
export class InternalAdminService {
  constructor(
    private readonly appService: AppService,
    private readonly userService: UserService,
    private readonly settingsService: SettingsService,
  ) {
  }

  async getUserSettingsToUpdateUserDOB(
    orgEnvId: string,
    { userId, dateOfBirth, location }: Omit<TGetUserSettings, 'productId'>,
  ) {
    if (!dateOfBirth) {
      throw new BadRequestException('dateOfBirth is missing');
    }
    const { credentials, productId } = await this.appService.getAppInfoByUser(orgEnvId, userId);
    const settings = this.settingsService.getUserSettings({ userId, dateOfBirth, location, productId }, credentials);

    await this.userService.updateDateOfBirth(orgEnvId, userId, new Date(dateOfBirth));
    return settings;
  }

  async deleteUserAccount(orgEnvId: string, userId: number) {
    const { credentials } = await this.appService.getAppInfoByUser(orgEnvId, userId);
    await this.userService.deleteUser(orgEnvId, userId, credentials);
  }
}
