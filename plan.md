# Plan for Implementing GET /internal-admin/users/ Endpoint

## Overview
Implement a new endpoint in the InternalAdminController that allows searching users by username with the following requirements:
- Query parameter: username (minimum 3 characters)
- Search type: Starts with match
- Return limit: 10 users
- Response format: Array of objects with username and parentEmail
- Authentication: Requires devportal-api scope

## Implementation Steps

1. [x] Create a new DTO for the response
   - Create UserSearchResponseDTO with username and parentEmail fields
   - Add proper Swagger/OpenAPI decorators

2. [x] Add new method to UserService
   - Create searchUsersByUsername method
   - Use TypeORM's Raw query to implement case-insensitive starts with search
   - Add limit of 10 results
   - Return array of users with their parent emails

3. [x] Add new endpoint to InternalAdminController
   - Add GET /internal-admin/users/ route
   - Add query parameter validation for username (min 3 chars)
   - Add proper Swagger/OpenAPI documentation
   - Add authentication guard with devportal-api scope
   - Call UserService.searchUsersByUsername
   - Map results to UserSearchResponseDTO

4. [x] Add tests
   - Add unit tests for UserService.searchUsersByUsername
   - Add e2e tests for the new endpoint
   - Test authentication requirements
   - Test username validation
   - Test response format and limits

## Technical Details

### Authentication
- Use existing KeycloakAuthGuard
- Add PoliciesGuard with HasAzp('devportal-api')

### Database Query
- Use TypeORM's Raw query with ILIKE for case-insensitive search
- Example: `username ILIKE :username%`
- Add limit(10) to query

### Response Format
```typescript
interface UserSearchResponseDTO {
  username: string;
  parentEmail: string;
}
```

### Error Cases to Handle
- Username less than 3 characters
- No users found
- Authentication failure
- Invalid scope 