import { INestApplication } from '@nestjs/common';
import { getDataSourceToken } from '@nestjs/typeorm';
import { SettingIdentifierDTO } from '@superawesome/freekws-settings-common';
import nock from 'nock';
import request from 'supertest';
import { DataSource } from 'typeorm';

import { App } from '../../src/app/app.entity';
import { User } from '../../src/user/user.entity';
import { createUser, Utils, voiceSetting } from '../utils';
import { UUID_REGEX } from '../utils/constants';
import { makeRequest } from '../utils/request-helper';

function setUpMocks() {
  Utils.mockAgeGateAPI();
  Utils.mockAnalyticServiceAPI();
  Utils.mockPreverificationServiceAPI();
  Utils.mockDevPortalAPI();
  Utils.mockSettingsBackendAPI();
}

describe('AppController (e2e)', () => {
  let app: INestApplication;
  let JWT_APP_TOKEN: string;
  let JWT_OTHER_APP_TOKEN: string;
  let JWT_MOBILE_APP_TOKEN: string;
  let dataSource: DataSource;
  let otherApp: App;
  let testApp: App;
  let unsupportedLangUser: User;

  beforeAll(async () => {
    app = await Utils.createTestServer();
    dataSource = app.get<DataSource>(getDataSourceToken());
    await Utils.cleanDb();
    const fixtures = await Utils.loadFixtures();
    otherApp = fixtures.App.find((app) => app.name === 'another-app') as App;
    testApp = fixtures.App.find((app) => app.name === 'test-app') as App;
    unsupportedLangUser = fixtures.User.find((user) => user.langauge === 'cn') as User;

    setUpMocks();

    JWT_APP_TOKEN = await Utils.getAppOAuthAppToken(testApp.id);
    JWT_OTHER_APP_TOKEN = await Utils.getAppOAuthAppToken(otherApp.id);
    JWT_MOBILE_APP_TOKEN = await Utils.getMobileAppOAuthToken(testApp.id);
  });

  jest.setTimeout(100000);

  afterAll(async () => {
    await Utils.stopTestServer(app);
    jest.clearAllMocks();
  });

  describe('POST /v2/apps/:appId/users', () => {
    it('should return expected response', async () => {
      const { body } = await request(app.getHttpServer())
        .post(`/v2/apps/${testApp.id}/users`)
        .set('Authorization', `Bearer ${JWT_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host)
        .send({
          country: 'US',
          dateOfBirth: '2014-10-10',
          parentEmail: '<EMAIL>',
          language: 'en',
          permissions: ['chat.voice'],
        })
        .expect(201);

      Utils.assertApiBody(body, {
        id: expect.any(Number),
        isMinor: true,
        uuid: expect.stringMatching(UUID_REGEX),
        permissions: {
          'chat.voice': true,
        },
      });
    });

    it('should respond with error for unauthenticated response', async () => {
      await request(app.getHttpServer())
        .post('/v2/apps/1852416260/users')
        .set('x-forwarded-host', testApp.orgEnv.host)
        .send({
          country: 'US',
          dateOfBirth: '2014-10-10',
          parentEmail: '<EMAIL>',
          language: 'en',
          permissions: ['chat.voice'],
        })
        .expect(400);
    });

    it('should respond with error for invalid token', async () => {
      await request(app.getHttpServer())
        .post('/v2/apps/1852416260/users')
        .set('Authorization', `Bearer ${JWT_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host)
        .send({
          country: 'US',
          dateOfBirth: '2014-10-10',
          parentEmail: '<EMAIL>',
          language: 'en',
          permissions: ['chat.voice'],
        })
        .expect(403);
    });

    it('should respond with error for minor user without parent email', async () => {
      const { body } = await request(app.getHttpServer())
        .post(`/v2/apps/${testApp.id}/users`)
        .set('Authorization', `Bearer ${JWT_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host)
        .send({
          country: 'US',
          dateOfBirth: '2014-10-10',
          language: 'en',
          permissions: ['chat.voice'],
        })
        .expect(400);

      expect(body).toMatchObject({
        code: 3,
        detail: ['parentEmail must be an email'],
        errorMessage: 'Bad Request Exception',
      });
    });
  });

  describe('POST /v2/apps/:appId/users/:userId/request-permissions (e2e)', () => {
    let USER_ID: number;

    beforeAll(async () => {
      USER_ID = await createUser(app, JWT_APP_TOKEN, testApp.id, testApp.orgEnv.host);
    });

    it('should return expected response', async () => {
      const { body } = await request(app.getHttpServer())
        .post(`/v2/apps/${testApp.id}/users/${USER_ID}/request-permissions`)
        .set('Authorization', `Bearer ${JWT_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host)
        .send({
          parentEmail: '<EMAIL>',
          permissions: ['chat.voice'],
        })
        .expect(200);

      expect(body).toEqual({
        permissions: {
          'chat.voice': true,
        },
        resultPermissions: {
          alreadyGrantedPerms: [
            {
              description: 'Allow user to use voice chat',
              displayName: 'Voice chat',
              name: 'chat.voice',
            },
          ],
          automaticallyGrantedPerms: [],
          missingPermissions: [],
        },
        user: {
          dateOfBirth: '2014-10-10',
          id: expect.any(Number),
          parentEmail: '[REDACTED]',
          isDeleted: false,
          language: 'en',
          parentDeleted: false,
          parentExpired: false,
          parentIdVerified: false,
          parentRejected: false,
          parentVerified: false,
          signUpCountry: 'US',
          username: null,
        },
      });
    });

    it('should respond with error for not exist permission', async () => {
      const { body } = await request(app.getHttpServer())
        .post(`/v2/apps/${testApp.id}/users/${USER_ID}/request-permissions`)
        .set('Authorization', `Bearer ${JWT_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host)
        .send({
          parentEmail: '<EMAIL>',
          permissions: ['chat.voice', 'default.unknown'],
        })
        .expect(404);

      expect(body).toEqual({
        code: 2,
        codeMeaning: 'notFound',
        errorMessage: 'Permissions not found: default.unknown',
      });
    });

    it('should respond with error for invalid token scope', async () => {
      const { body } = await request(app.getHttpServer())
        .post(`/v2/apps/${testApp.id}/users/${USER_ID}/request-permissions`)
        .set('Authorization', `Bearer ${JWT_MOBILE_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host)
        .send({})
        .expect(403);

      expect(body).toEqual({
        code: 1,
        codeMeaning: 'forbidden',
        errorMessage: 'operation not supported for this user or client',
      });
    });

    it('should respond with error for invalid app ID', async () => {
      await request(app.getHttpServer())
        .post(`/v2/apps/1800000000/users/${USER_ID}/request-permissions`)
        .set('Authorization', `Bearer ${JWT_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host)
        .send({})
        .expect(403);
    });

    it('should respond with error for missing parent email', async () => {
      const { body } = await request(app.getHttpServer())
        .post(`/v2/apps/${testApp.id}/users/${USER_ID}/request-permissions`)
        .set('Authorization', `Bearer ${JWT_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host)
        .send({
          permissions: ['chat.voice'],
        })
        .expect(400);

      expect(body).toEqual({
        code: 3,
        codeMeaning: 'badRequest',
        errorMessage: 'No parent email.',
      });
    });

    it('should respond with error for graduated user', async () => {
      const { body } = await request(app.getHttpServer())
        .post(`/v2/apps/${testApp.id}/users/${USER_ID}/request-permissions`)
        .set('Authorization', `Bearer ${JWT_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host)
        .send({
          dateOfBirth: '2000-01-01',
          permissions: ['chat.voice'],
        })
        .expect(403);

      expect(body).toEqual({
        code: 1,
        codeMeaning: 'forbidden',
        errorMessage: 'operation requested is not permitted for graduated users',
      });
    });

    it('should automatically add T&C permission when termsAndConditionsRequired is true and user has not accepted T&C', async () => {
      nock.cleanAll();
      Utils.mockAgeGateAPI();
      try {
        Utils.mockGetUserSettingsWithVoiceSetting();
        const testAppTermsRequired = await Utils.appRepo.save({
          ...testApp,
          termsAndConditionsRequired: true,
        });

        const settingsServiceBaseUrl = Utils.configService.getSettingsService().baseURL;
        nock(settingsServiceBaseUrl)
          .post(`/v1/settings/admin/users/${USER_ID}/products/${testAppTermsRequired.productId}/send-consent-email`)
          .reply(200, function (uri, requestBody) {
            let requestBodyOfConsentEmail: { settings: SettingIdentifierDTO[] } | null = null;
            if (typeof requestBody !== 'string') {
              requestBodyOfConsentEmail = requestBody as { settings: SettingIdentifierDTO[] };
            }
            expect(requestBodyOfConsentEmail?.settings).toStrictEqual([
              {
                namespace: 'terms',
                settingName: 'app-terms-and-conditions',
              },
              {
                namespace: 'chat',
                settingName: 'voice',
              },
            ]);
            return {
              response: {
                settings: [],
              },
            };
          });

        await request(app.getHttpServer())
          .post(`/v2/apps/${testAppTermsRequired.id}/users/${USER_ID}/request-permissions`)
          .set('Authorization', `Bearer ${JWT_APP_TOKEN}`)
          .set('x-forwarded-host', testApp.orgEnv.host)
          .send({
            parentEmail: '<EMAIL>',
            permissions: ['chat.voice'],
          })
          .expect(200);
      } finally {
        nock.cleanAll();
        setUpMocks();
      }
    });
  });

  describe('POST /v2/apps/:appId/users/:userId/update-parent-email (e2e)', () => {
    let USER_ID: number;

    beforeAll(async () => {
      USER_ID = await createUser(app, JWT_APP_TOKEN, testApp.id, testApp.orgEnv.host);
    });

    it('should return expected response code', async () => {
      await request(app.getHttpServer())
        .post(`/v2/apps/${testApp.id}/users/${USER_ID}/update-parent-email`)
        .set('Authorization', `Bearer ${JWT_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host)
        .send({
          parentEmail: '<EMAIL>',
        })
        .expect(204);
    });

    it('should respond with error for invalid token scope', async () => {
      const { body } = await request(app.getHttpServer())
        .post(`/v2/apps/${testApp.id}/users/${USER_ID}/update-parent-email`)
        .set('Authorization', `Bearer ${JWT_MOBILE_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host)
        .send({})
        .expect(403);

      expect(body).toEqual({
        code: 1,
        codeMeaning: 'forbidden',
        errorMessage: 'operation not supported for this user or client',
      });
    });

    it('should respond with error for invalid app ID', async () => {
      await request(app.getHttpServer())
        .post(`/v2/apps/1800000000/users/${USER_ID}/update-parent-email`)
        .set('Authorization', `Bearer ${JWT_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host)
        .send({})
        .expect(403);
    });
  });

  describe('GET /v2/apps/:appId/users/:userId', () => {
    let USER_ID: number;
    let VERIFIED_USER_ID: number;

    beforeAll(async () => {
      USER_ID = await createUser(app, JWT_APP_TOKEN, testApp.id, testApp.orgEnv.host);

      const { body: verifiedBody } = await request(app.getHttpServer())
        .post(`/v2/apps/${testApp.id}/users`)
        .set('Authorization', `Bearer ${JWT_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host)
        .send({
          country: 'US',
          dateOfBirth: '2024-10-10',
          parentEmail: '<EMAIL>',
          language: 'en',
          permissions: ['chat.voice'],
        });
      expect(verifiedBody.id).toBeGreaterThan(1000000000);

      VERIFIED_USER_ID = verifiedBody.id;
    });
    beforeEach(() => {
      Utils.mockFamilyServiceAPI(1, VERIFIED_USER_ID);
    });

    it('should return expected response for app scope and unverified parent', async () => {
      Utils.mockFamilyServiceGuardianRequest();

      const { body } = await request(app.getHttpServer())
        .get(`/v2/apps/${testApp.id}/users/${USER_ID}`)
        .set('Authorization', `Bearer ${JWT_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host)
        .expect(200);

      expect(body).toEqual({
        id: expect.any(Number),
        dateOfBirth: '2014-10-10',
        displayName: null,
        language: 'en',
        permissions: {
          'chat.voice': null,
        },
        activationCreatedAt: expect.stringMatching(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/),
        createdAt: expect.stringMatching(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/),
        consentAgeForCountry: 13,
        isMinor: true,
        username: null,
        appData: [],
        city: null,
        country: null,
        email: null,
        firstName: null,
        lastName: null,
        postalCode: null,
        streetAddress: null,
        parentDetails: {
          oauthProvider: null,
          usedVerificationMethodName: 'KWS',
        },
      });
    });

    it('should return expected response for control panel scope and verified parent', async () => {
      const parentEmail = '<EMAIL>';
      Utils.mockFamilyServiceGuardianRequest(parentEmail);
      const { body } = await request(app.getHttpServer())
        .get(`/v2/apps/${testApp.id}/users/${VERIFIED_USER_ID}`)
        .set('Authorization', `Bearer ${JWT_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host)
        .expect(200);

      expect(body).toEqual({
        id: expect.any(Number),
        dateOfBirth: '2024-10-10',
        language: 'en',
        permissions: {
          'chat.voice': null,
        },
        parentEmail: parentEmail,
        parentState: {
          deleted: false,
          expired: false,
          idVerified: false,
          rejected: false,
          verified: false,
        },
        activationCreatedAt: expect.stringMatching(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/),
        createdAt: expect.stringMatching(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/),
        consentAgeForCountry: 13,
        isMinor: true,
        username: null,
        displayName: null,
        appData: [],
        city: null,
        country: null,
        email: null,
        firstName: null,
        lastName: null,
        postalCode: null,
        streetAddress: null,
        parentDetails: {
          oauthProvider: null,
          usedVerificationMethodName: 'KWS',
        },
      });
    });

    it('should return default language for user with unsupported language', async () => {
      Utils.mockFamilyServiceGuardianRequest();
      const { body } = await request(app.getHttpServer())
        .get(`/v2/apps/${testApp.id}/users/${unsupportedLangUser.id}`)
        .set('Authorization', `Bearer ${JWT_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host)
        .expect(200);

      expect(body).toEqual({
        id: expect.any(Number),
        dateOfBirth: '2020-11-17',
        language: 'en',
        permissions: {
          'chat.voice': null,
        },
        activationCreatedAt: expect.stringMatching(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/),
        createdAt: expect.stringMatching(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/),
        consentAgeForCountry: 13,
        isMinor: true,
        username: null,
        displayName: null,
        appData: [],
        city: null,
        country: null,
        email: null,
        firstName: null,
        lastName: null,
        postalCode: null,
        streetAddress: null,
        parentDetails: {
          oauthProvider: null,
          usedVerificationMethodName: 'KWS',
        },
      });
    });

    it('should respond with error for invalid token scope', async () => {
      const { body } = await request(app.getHttpServer())
        .get(`/v2/apps/${testApp.id}/users/${USER_ID}`)
        .set('Authorization', `Bearer ${JWT_MOBILE_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host)
        .expect(403);

      expect(body).toEqual({
        code: 1,
        codeMeaning: 'forbidden',
        errorMessage: 'operation not supported for this user or client',
      });
    });
  });

  describe('DELETE /v2/apps/:appId/users/:userId', () => {
    let USER_ID = -1;

    beforeAll(async () => {
      USER_ID = await createUser(app, JWT_APP_TOKEN, testApp.id, testApp.orgEnv.host);
    });

    it("deletes user's settings", async () => {
      Utils.mockDeleteUserSettings();
      const userId = await createUser(app, JWT_APP_TOKEN, testApp.id, testApp.orgEnv.host);
      await Utils.createUserActivation(USER_ID, otherApp.id);
      await request(app.getHttpServer())
        .delete(`/v2/apps/${testApp.id}/users/${userId}`)
        .set('Authorization', `Bearer ${JWT_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host)
        .expect(204);
    });

    it("404's when attempting to delete a user without the specified activation", async () => {
      await request(app.getHttpServer())
        .delete(`/v2/apps/${testApp.id}/users/2468`)
        .set('Authorization', `Bearer ${JWT_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host)
        .expect(404);
    });

    it('deletes user when it is the users final activation', async () => {
      Utils.mockDeleteUserSettings();
      const userId = await createUser(app, JWT_APP_TOKEN, testApp.id, testApp.orgEnv.host);

      await request(app.getHttpServer())
        .delete(`/v2/apps/${testApp.id}/users/${userId}`)
        .set('Authorization', `Bearer ${JWT_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host)
        .expect(204);

      const userAfter = await dataSource.getRepository(User).findOne({ where: { id: userId } });
      expect(userAfter).toBeNull();
    });
  });

  describe('GET /v2/apps/:appId/users/:userId/permissions', () => {
    let USER_ID = -1;

    beforeAll(async () => {
      USER_ID = await createUser(app, JWT_APP_TOKEN, testApp.id, testApp.orgEnv.host);
    });
    beforeEach(() => {
      Utils.mockFamilyServiceAPI();
    });

    it('returns expected permissions for specified user', async () => {
      Utils.mockFamilyServiceGuardianRequest('<EMAIL>');
      const response = await request(app.getHttpServer())
        .get(`/v2/apps/${testApp.id}/users/${USER_ID}/permissions`)
        .set('Authorization', `Bearer ${JWT_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host);

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        'chat.voice': null,
      });
    });

    it('returns expected permissions for specified user with extended details', async () => {
      Utils.mockFamilyServiceGuardianRequest('<EMAIL>');
      const response = await request(app.getHttpServer())
        .get(`/v2/apps/${testApp.id}/users/${USER_ID}/permissions?extended=true`)
        .set('Authorization', `Bearer ${JWT_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host);

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        permissions: {
          'chat.voice': null,
        },
        disabledPermissions: [],
        isGraduated: false,
      });
    });
  });

  describe('POST /v2/apps/:appId/users/:userId/review-permissions', () => {
    let USER_ID = -1;

    beforeAll(async () => {
      USER_ID = await createUser(app, JWT_APP_TOKEN, testApp.id, testApp.orgEnv.host);
    });

    it('return expected response', async () => {
      nock.cleanAll();

      const { baseURL } = Utils.configService.getSettingsService();
      nock(baseURL)
        .get(/\/v1\/settings\/users\/.*\/products\/.*\/values/g)
        .reply(200, {
          response: {
            settings: [voiceSetting],
          },
        })
        .post(/\/v1\/settings\/admin\/users\/[^/]+\/products\/[^/]+\/send-consent-email$/)
        .reply(200, {
          response: { settings: [] },
        });

      const response = await request(app.getHttpServer())
        .post(`/v2/apps/${testApp.id}/users/${USER_ID}/review-permissions`)
        .set('Authorization', `Bearer ${JWT_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host);

      expect(response.status).toBe(201);
      setUpMocks();
    });
  });

  describe('POST /v2/apps/:appId/users/:userId/activate', () => {
    let USER_ID: number;
    let OTHER_APP_ID: number;

    beforeAll(async () => {
      OTHER_APP_ID = otherApp.id;
    });

    beforeEach(async () => {
      USER_ID = await createUser(app, JWT_APP_TOKEN, testApp.id, testApp.orgEnv.host);
      nock.cleanAll();
      setUpMocks();
    });

    afterEach(() => {
      nock.cleanAll();
      setUpMocks();
    });

    it('should activate a user to another app successfully', async () => {
      Utils.mockFamilyServiceGuardianRequest('<EMAIL>');
      Utils.mockAnalyticServiceV1EventsAPI();

      const { body } = await makeRequest(app, 'post', `/v2/apps/${OTHER_APP_ID}/users/${USER_ID}/activate`, {
        headers: {
          Authorization: `Bearer ${JWT_OTHER_APP_TOKEN}`,
          'x-forwarded-host': otherApp.orgEnv.host,
        },
        body: {
          permissions: ['chat.voice'],
        },
        expectedStatus: 200,
      });

      expect(body).toEqual({
        id: USER_ID,
        permissions: {
          'chat.voice': null,
        },
      });

      const activationAfter = await dataSource.getRepository('Activation').findOne({
        where: {
          userId: USER_ID,
          appId: OTHER_APP_ID,
        },
      });

      expect(activationAfter).not.toBeNull();
    });

    it('should return 409 Conflict if user is already activated for the app', async () => {
      await Utils.createUserActivation(USER_ID, OTHER_APP_ID);

      const { body } = await makeRequest(app, 'post', `/v2/apps/${OTHER_APP_ID}/users/${USER_ID}/activate`, {
        headers: {
          Authorization: `Bearer ${JWT_OTHER_APP_TOKEN}`,
          'x-forwarded-host': testApp.orgEnv.host,
        },
        body: {
          permissions: ['chat.voice'],
        },
        expectedStatus: 409,
      });

      expect(body).toMatchObject({
        codeMeaning: 'conflict',
        errorMessage: 'App already activated',
      });
    });

    it('should activate user with requested permissions', async () => {
      await Utils.activationRepo.delete({ userId: USER_ID, appId: OTHER_APP_ID });
      Utils.mockFamilyServiceGuardianRequest('<EMAIL>');
      Utils.mockAnalyticServiceV1EventsAPI();
      Utils.mockSettingsSendConsentEmail();

      const { body } = await makeRequest(app, 'post', `/v2/apps/${OTHER_APP_ID}/users/${USER_ID}/activate`, {
        headers: {
          Authorization: `Bearer ${JWT_OTHER_APP_TOKEN}`,
          'x-forwarded-host': testApp.orgEnv.host,
        },
        body: {
          permissions: ['chat.voice', 'chat.text'],
        },
        expectedStatus: 200,
      });

      expect(body).toEqual({
        id: USER_ID,
        permissions: expect.objectContaining({
          'chat.voice': null,
          'chat.text': null,
        }),
      });
    });

    it('should respond with error for invalid token', async () => {
      const { body } = await makeRequest(app, 'post', `/v2/apps/${OTHER_APP_ID}/users/${USER_ID}/activate`, {
        headers: {
          Authorization: 'Bearer invalid_token',
          'x-forwarded-host': testApp.orgEnv.host,
        },
        body: {
          permissions: ['chat.voice'],
        },
        expectedStatus: 401,
      });

      expect(body).toMatchObject({
        errorMessage: 'The access token provided is invalid.',
      });
    });

    it('should respond with error for invalid app ID', async () => {
      const invalidAppId = 9999999;

      await makeRequest(app, 'post', `/v2/apps/${invalidAppId}/users/${USER_ID}/activate`, {
        headers: {
          Authorization: `Bearer ${JWT_APP_TOKEN}`,
          'x-forwarded-host': testApp.orgEnv.host,
        },
        body: {
          permissions: ['chat.voice'],
        },
        expectedStatus: 403,
      });
    });

    it('should respond with error for non-existent user ID', async () => {
      const nonExistentUserId = 9999999;

      await makeRequest(app, 'post', `/v2/apps/${OTHER_APP_ID}/users/${nonExistentUserId}/activate`, {
        headers: {
          Authorization: `Bearer ${JWT_OTHER_APP_TOKEN}`,
          'x-forwarded-host': testApp.orgEnv.host,
        },
        body: {
          permissions: ['chat.voice'],
        },
        expectedStatus: 404,
      });
    });
  });

  describe('GET /v2/apps/:appId/:userId', () => {
    let userId: number;

    beforeEach(async () => {
      userId = await createUser(app, JWT_APP_TOKEN, testApp.id, testApp.orgEnv.host);
      nock.cleanAll();
      setUpMocks();
    });

    it('should return expected response', async () => {
      const { body } = await request(app.getHttpServer())
        .get(`/v2/apps/${testApp.id}/${userId}`)
        .set('Authorization', `Bearer ${JWT_APP_TOKEN}`)
        .set('x-forwarded-host', testApp.orgEnv.host)
        .expect(200);

      expect(body).toEqual({
        activations: [
          {
            activationId: '1000000023',
            createdAt: expect.any(String),
            permissions: expect.objectContaining({
              'chat.voice': null,
            }),
            status: 'Active',
          },
        ],
        appId: 1852416263,
        userId: 1993416482,
      });
    });
  });

  describe('GET /v2/apps/:appId/permissions/translated', () => {
    beforeEach(() => {
      nock.cleanAll();
      setUpMocks();
    });

    afterEach(() => {
      nock.cleanAll();
      setUpMocks();
    });

    function mockKeycloakTokenEndpoint() {
      const keycloakConfig = Utils.configService.getKeycloak();
      nock(keycloakConfig.authServerUrl).post('/realms/kws/protocol/openid-connect/token').reply(200, {
        access_token: 'mock-access-token',
        token_type: 'Bearer',
        expires_in: 3600,
      });
    }

    function mockSettingsDefinitionsEndpoint(yamlContent: string) {
      const settingsBaseUrl = Utils.configService.getSettingsService().baseURL;
      return nock(settingsBaseUrl)
        .get(/\/v1\/set{2}ings\/internal-admin\/orgs\/.*\/org-env(?:s\/.*\/product){2}-envs\/.*\/definitions/)
        .reply(200, {
          response: {
            definitions: [yamlContent],
          },
        });
    }

    it('should return translated permissions for all permissions when no specific permissions requested', async () => {
      mockKeycloakTokenEndpoint();
      mockSettingsDefinitionsEndpoint(`version: 1
orgId: test-org-id
productId: product-id
namespace: chat
settings:
  - settingName: voice
    valueType: boolean
    userHidden: false
    required: false
    autoReviewConsent: false
    label:
      en: Voice Chat
    parentNotice:
      en: Allow voice chat
    userNotice:
      en: Voice chat permission
    regions: []`);

      const { body } = await makeRequest(app, 'get', `/v2/apps/${testApp.id}/permissions/translated`, {
        headers: {
          'x-forwarded-host': testApp.orgEnv.host,
        },
        expectedStatus: 200,
      });

      expect(body).toEqual([
        {
          name: 'chat.voice',
          displayName: 'Voice Chat',
          childFacingDescription: 'Voice chat permission',
          privacyNotice: 'Allow voice chat',
        },
      ]);
    });

    it('should filter permissions when specific permissions are requested', async () => {
      mockKeycloakTokenEndpoint();
      mockSettingsDefinitionsEndpoint(`version: 1
orgId: test-org-id
productId: product-id
namespace: chat
settings:
  - settingName: voice
    valueType: boolean
    userHidden: false
    required: false
    autoReviewConsent: false
    label:
      en: Voice Chat
    parentNotice:
      en: Allow voice chat
    userNotice:
      en: Voice chat permission
    regions: []`);

      const { body } = await makeRequest(
        app,
        'get',
        `/v2/apps/${testApp.id}/permissions/translated?permissions=chat.voice`,
        {
          headers: {
            'x-forwarded-host': testApp.orgEnv.host,
          },
          expectedStatus: 200,
        },
      );

      expect(body).toEqual([
        {
          name: 'chat.voice',
          displayName: 'Voice Chat',
          childFacingDescription: 'Voice chat permission',
          privacyNotice: 'Allow voice chat',
        },
      ]);
    });

    it('should use Spanish translations when accept-language header is es', async () => {
      mockKeycloakTokenEndpoint();
      mockSettingsDefinitionsEndpoint(`version: 1
orgId: test-org-id
productId: product-id
namespace: chat
settings:
  - settingName: voice
    valueType: boolean
    userHidden: false
    required: false
    autoReviewConsent: false
    label:
      en: Voice Chat
      es: Chat de Voz
    parentNotice:
      en: Allow voice chat
      es: Permitir chat de voz
    userNotice:
      en: Voice chat permission
      es: Permiso de chat de voz
    regions: []`);

      const { body } = await makeRequest(
        app,
        'get',
        `/v2/apps/${testApp.id}/permissions/translated?permissions=chat.voice`,
        {
          headers: {
            'accept-language': 'es',
            'x-forwarded-host': testApp.orgEnv.host,
          },
          expectedStatus: 200,
        },
      );

      expect(body).toEqual([
        {
          name: 'chat.voice',
          displayName: 'Chat de Voz',
          childFacingDescription: 'Permiso de chat de voz',
          privacyNotice: 'Permitir chat de voz',
        },
      ]);
    });

    it('should handle CloudFront-Viewer-Country header', async () => {
      mockKeycloakTokenEndpoint();
      mockSettingsDefinitionsEndpoint(`version: 1
orgId: test-org-id
productId: product-id
namespace: chat
settings:
  - settingName: voice
    valueType: boolean
    userHidden: false
    required: false
    autoReviewConsent: false
    label:
      en: Voice Chat
    parentNotice:
      en: Allow voice chat
    userNotice:
      en: Voice chat permission
    regions: []`);

      const { body } = await makeRequest(
        app,
        'get',
        `/v2/apps/${testApp.id}/permissions/translated?permissions=chat.voice`,
        {
          headers: {
            'CloudFront-Viewer-Country': 'us',
            'x-forwarded-host': testApp.orgEnv.host,
          },
          expectedStatus: 200,
        },
      );

      expect(body).toEqual([
        {
          name: 'chat.voice',
          displayName: 'Voice Chat',
          childFacingDescription: 'Voice chat permission',
          privacyNotice: 'Allow voice chat',
        },
      ]);
    });

    it('should return empty array when requested permissions do not exist', async () => {
      mockKeycloakTokenEndpoint();
      mockSettingsDefinitionsEndpoint(`version: 1
orgId: test-org-id
productId: product-id
namespace: chat
settings:
  - settingName: voice
    valueType: boolean
    userHidden: false
    required: false
    autoReviewConsent: false
    label:
      en: Voice Chat
    parentNotice:
      en: Allow voice chat
    userNotice:
      en: Voice chat permission
    regions: []`);

      const { body } = await makeRequest(
        app,
        'get',
        `/v2/apps/${testApp.id}/permissions/translated?permissions=nonexistent.permission`,
        {
          headers: {
            'x-forwarded-host': testApp.orgEnv.host,
          },
          expectedStatus: 200,
        },
      );

      expect(body).toEqual([]);
    });
  });
});
